/* Customer Details Page Styles - Simplified Design */

.customerDetailsContainer {
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e2e8f0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
}

.breadcrumb a {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.breadcrumb a:hover {
  color: #2563eb;
}

.breadcrumb span {
  color: #94a3b8;
}

.headerActions {
  display: flex;
  gap: 0.75rem;
}

.editButton, .bookButton, .deleteButton, .backButton {
  padding: 0.625rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  border: none;
  font-size: 0.875rem;
}

.editButton {
  background: #3b82f6;
  color: white;
}

.bookButton {
  background: #10b981;
  color: white;
}

.deleteButton {
  background: #ef4444;
  color: white;
}

.backButton {
  background: #64748b;
  color: white;
}

.editButton:hover {
  background: #2563eb;
}

.bookButton:hover {
  background: #059669;
}

.deleteButton:hover {
  background: #dc2626;
}

.backButton:hover {
  background: #475569;
}

/* Primary Information Section */
.primaryInfo {
  background: white;
  padding: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.customerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.customerDetails {
  flex: 1;
  margin-left: 1rem;
}

.customerName {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.customerEmail {
  color: #64748b;
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
}

.customerPhone {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

.customerStatus {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
}

.statusBadge {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.statusActive {
  background: #dcfce7;
  color: #166534;
}

.statusNew {
  background: #dbeafe;
  color: #1e40af;
}

.quickStats {
  display: flex;
  gap: 1.5rem;
}

.statItem {
  text-align: center;
}

.statValue {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Tabbed Interface */
.tabbedContent {
  background: white;
  max-width: 1200px;
  margin: 0 auto;
}

.tabNavigation {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 2rem;
}

.tabButton {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tabButton:hover {
  color: #3b82f6;
}

.tabButton.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.tabContent {
  padding: 2rem;
}

/* Overview Tab */
.overviewTab {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.quickActions h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.actionButtons {
  display: flex;
  gap: 0.75rem;
}

.actionButton {
  flex: 1;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #3b82f6;
  color: white;
}

.recentActivity h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.emptyState {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.createBookingBtn {
  background: #10b981;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  margin-top: 1rem;
  display: inline-block;
}

.createBookingBtn:hover {
  background: #059669;
}

.customerAvatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Booking Lists */
.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bookingItem {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.serviceName {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.bookingStatus {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.bookingDetails {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #64748b;
}

.viewAllBtn {
  background: none;
  border: none;
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  padding: 0.75rem;
  text-align: center;
  margin-top: 0.5rem;
}

.viewAllBtn:hover {
  color: #2563eb;
}

/* Bookings Tab */
.bookingsTab h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.allBookingsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bookingCard {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
}

.serviceInfo h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.serviceInfo p {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

.bookingMeta {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: #64748b;
}

/* Details Tab */
.detailsTab {
  max-width: 800px;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detailCard {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.detailCard h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.contactInfo, .personalInfo {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contactItem, .infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.contactItem strong, .infoItem strong {
  color: #374151;
  font-weight: 600;
}

.notes {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
  font-size: 0.875rem;
}

.metaInfo {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
  color: #64748b;
  font-size: 0.875rem;
}

.metaItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.quickActions, .bookingHistory {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quickActions h3, .bookingHistory h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.actionButton {
  display: block;
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.actionButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
}

.noBookings {
  color: #64748b;
  font-style: italic;
  text-align: center;
  padding: 2rem 0;
}

.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bookingItem {
  background: #f8fafc;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.serviceName {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.bookingStatus {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.bookingDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #64748b;
}

.viewAllBookings {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.viewAllBookings:hover {
  color: #764ba2;
}

/* Loading and Error States */
.loadingContainer, .errorContainer, .notFoundContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h2, .notFoundContainer h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .headerActions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .primaryInfo {
    padding: 1rem;
  }

  .customerHeader {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .customerDetails {
    margin-left: 0;
  }

  .customerStatus {
    align-items: center;
  }

  .quickStats {
    flex-direction: column;
    gap: 1rem;
  }

  .tabNavigation {
    padding: 0 1rem;
    overflow-x: auto;
  }

  .tabContent {
    padding: 1rem;
  }

  .overviewTab {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .actionButtons {
    flex-direction: column;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }

  .bookingMeta {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .customerAvatar {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }

  .customerName {
    font-size: 1.5rem;
  }

  .tabButton {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}
